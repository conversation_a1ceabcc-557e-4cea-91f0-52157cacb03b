import React, { useState, useEffect } from 'react';
import { YTHDatePicker, YTHForm, YTHPickUser, YTHToast, YTHLocalization } from 'yth-ui';
import ExceptionRecordsApi from '@/service/exceptionRecordsApi';
import {
  message,
  Button,
  Spin,
  Input,
  Form,
  Select,
  DatePicker,
  Col,
  Row,
  Card,
} from 'antd';
import { CurrentUser, Token } from '@/Constant';
import baseApi from '@/service/baseApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { ArrowLeftOutlined } from '@ant-design/icons';
import locales from '@/locales';

import { BaseResponse } from '@/types/common';
import {
  ExceptionRecordsVo,
  ExceptionRecordsInsertParam,
  ExceptionRecordsUpdateParam,
} from '@/types/exceptionRecords';
import moment from 'moment';
import { Unit, User } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import style from './exceptionRecords.module.less';

const { TextArea } = Input;

/**
 * @description 异常记录表单页面
 */
const ExceptionRecordsForm: React.FC<{ history?: any; location?: any }> = ({
  history,
  location,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [mode, setMode] = useState<string>('add'); // add, edit, view
  const [recordId, setRecordId] = useState<string>('');
  const [exceptionTypeOptions, setExceptionTypeOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);
  const [handleStatusOptions, setHandleStatusOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);

  // 表单
  const [form] = Form.useForm();

  // 解析URL参数
  useEffect(() => {
    if (location?.search) {
      const params = new URLSearchParams(location.search);
      const modeParam = params.get('mode') || 'add';
      const idParam = params.get('id') || '';
      setMode(modeParam);
      setRecordId(idParam);
    }
  }, [location]);

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    if (!recordId) return;
    setIsLoading(true);
    try {
      const res: BaseResponse<ExceptionRecordsVo> = await ExceptionRecordsApi.getDetailById({
        id: recordId,
      });
      if (res && res.code && res.code === 200) {
        const formD: ExceptionRecordsVo = res.data;

        // 处理时间组件的数据格式
        const processedFormD: ExceptionRecordsVo = { ...formD };
        if (formD.exceptionTime) {
          processedFormD.exceptionTime = moment(formD.exceptionTime);
        }
        if (formD.handleTime) {
          processedFormD.handleTime = moment(formD.handleTime);
        }

        form.setFieldsValue(processedFormD);
      } else {
        message.error('获取异常记录详情失败');
      }
    } catch {
      message.error('获取异常记录详情失败');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (mode === 'edit' || mode === 'view') {
      queryDataDetail();
    }
  }, [mode, recordId]);

  // 加载字典数据
  useEffect(() => {
    const loadDictionaries: () => Promise<void> = async () => {
      try {
        const [exceptionTypes] = await Promise.all([
          baseApi.getDictionary(dicParams.EXCEPTION_TYPE),
        ]);

        setExceptionTypeOptions(
          exceptionTypes.map((item) => ({ label: item.text, value: item.code })),
        );
        setHandleStatusOptions([
          { label: '未处置', value: '0' },
          { label: '已处置', value: '1' },
        ]);
      } catch (error) {
        console.error('加载字典数据失败:', error);
      }
    };

    loadDictionaries();
  }, []);

  // 设置页面标题
  const getPageTitle: () => string = (): string => {
    if (mode === 'add') {
      return '新增异常记录';
    } else if (mode === 'view') {
      return '查看异常记录';
    } else if (mode === 'edit') {
      return '编辑异常记录';
    }
    return '异常记录';
  };

  // 返回列表页面
  const goBack: () => void = () => {
    if (history) {
      history.push('/exceptionRecords/list');
    }
  };

  // 处理提交数据的公共方法
  const processSubmitData: (
    data: ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam,
  ) => ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam = (
    data: ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam,
  ): ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam => {
    const submitData: ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam = {
      ...data,
    };

    // 处理时间组件的数据格式
    if (data.exceptionTime) {
      submitData.exceptionTime = moment(data.exceptionTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.handleTime) {
      submitData.handleTime = moment(data.handleTime).format('YYYY-MM-DD HH:mm:ss');
    }

    return submitData;
  };

  // 新增保存
  const submitAddData: (data: ExceptionRecordsInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const submitData: ExceptionRecordsInsertParam = processSubmitData(
      data,
    ) as ExceptionRecordsInsertParam;
    const res: BaseResponse<object> = await ExceptionRecordsApi.insert(submitData);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      goBack();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: ExceptionRecordsUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const processedData: ExceptionRecordsUpdateParam = processSubmitData(
      data,
    ) as ExceptionRecordsUpdateParam;
    const submitData: ExceptionRecordsUpdateParam = {
      ...processedData,
      id: recordId,
    };
    const res: BaseResponse<object> = await ExceptionRecordsApi.update(submitData);
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      goBack();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    try {
      const values: ExceptionRecordsInsertParam | ExceptionRecordsUpdateParam =
        await form.validateFields();
      const submitData: ExceptionRecordsInsertParam = JSON.parse(JSON.stringify(values));
      if (mode === 'add') {
        submitAddData(submitData);
        return;
      }
      submitEditData(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 点击取消
  const cancel: () => void = () => {
    form.resetFields();
    goBack();
  };

  const isReadOnly = mode === 'view';

  return (
    <div style={{ width: '100%', height: '100%', padding: '20px' }}>
      <Spin spinning={isLoading}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={goBack}
                style={{ marginRight: '10px' }}
              >
                返回
              </Button>
              {getPageTitle()}
            </div>
          }
          extra={
            !isReadOnly && (
              <div>
                <Button onClick={cancel} style={{ marginRight: '10px' }}>
                  取消
                </Button>
                <Button type="primary" onClick={save}>
                  保存
                </Button>
              </div>
            )
          }
        >
          <div className={style['exception-records-form']}>
            <Form
              form={form}
              size="small"
              colon={false}
              labelCol={{ span: 6 }}
              className={style['form']}
            >
              <Form.Item name="id" label="id" hidden>
                <Input disabled />
              </Form.Item>
              <Row>
                <Col span={12}>
                  <Form.Item
                    name="taskId"
                    label="任务ID"
                    rules={[{ required: true, message: '请输入任务ID' }]}
                  >
                    <Input disabled={isReadOnly} placeholder="请输入任务ID" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="taskPointId" label="检查点ID">
                    <Input disabled={isReadOnly} placeholder="请输入检查点ID" />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item
                    name="exceptionType"
                    label="异常类型"
                    rules={[{ required: true, message: '请选择异常类型' }]}
                  >
                    <Select
                      disabled={isReadOnly}
                      options={exceptionTypeOptions}
                      placeholder="请选择异常类型"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="exceptionTime"
                    label="异常发生时间"
                    rules={[{ required: true, message: '请选择异常发生时间' }]}
                  >
                    <DatePicker
                      showTime
                      format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择异常发生时间"
                      disabled={isReadOnly}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item
                    name="exceptionDescribe"
                    label="异常描述"
                    labelCol={{ span: 3 }}
                    rules={[{ required: true, message: '请输入异常描述' }]}
                  >
                    <TextArea
                      disabled={isReadOnly}
                      placeholder="请输入异常描述"
                      rows={3}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item name="reportUserId" label="上报人ID">
                    <Input disabled={isReadOnly} placeholder="请输入上报人ID" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="reportUserName" label="上报人姓名">
                    <Input disabled={isReadOnly} placeholder="请输入上报人姓名" />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item name="handleStatus" label="处置状态">
                    <Select
                      disabled={isReadOnly}
                      options={handleStatusOptions}
                      placeholder="请选择处置状态"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="handleUserId" label="处置人ID">
                    <Input disabled={isReadOnly} placeholder="请输入处置人ID" />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Form.Item name="handleUserName" label="处置人姓名">
                    <Input disabled={isReadOnly} placeholder="请输入处置人姓名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="handleTime" label="处置时间">
                    <DatePicker
                      showTime
                      format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择处置时间"
                      disabled={isReadOnly}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item name="handleMethod" label="处置方式" labelCol={{ span: 3 }}>
                    <TextArea
                      disabled={isReadOnly}
                      placeholder="请输入处置方式"
                      rows={3}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item name="handleFiles" label="处置附件" labelCol={{ span: 3 }}>
                    <Input disabled={isReadOnly} placeholder="请输入处置附件" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default YTHLocalization.withLocal(
  ExceptionRecordsForm,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
