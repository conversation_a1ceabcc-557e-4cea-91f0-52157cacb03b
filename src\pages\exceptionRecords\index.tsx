import React, { useState, useEffect } from 'react';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import ExceptionRecordsList from './list';
import ExceptionRecordsForm from './form';

/**
 * @description 异常记录管理路由组件
 */
interface ExceptionRecordsRouteProps {
  history?: any;
  location?: any;
}

const ExceptionRecordsRoute: React.FC<ExceptionRecordsRouteProps> = ({ history, location }) => {
  const [currentView, setCurrentView] = useState<'list' | 'form'>('list');
  const [mockLocation, setMockLocation] = useState<any>({
    pathname: '/exceptionRecords/list',
    search: '',
  });

  // 创建一个模拟的 history 对象来处理路由
  const mockHistory = {
    push: (path: string) => {
      if (path.includes('/form')) {
        setCurrentView('form');
        const [pathname, search] = path.split('?');
        setMockLocation({
          pathname,
          search: search ? `?${search}` : '',
        });
      } else {
        setCurrentView('list');
        setMockLocation({
          pathname: '/exceptionRecords/list',
          search: '',
        });
      }
    },
    goBack: () => {
      setCurrentView('list');
      setMockLocation({
        pathname: '/exceptionRecords/list',
        search: '',
      });
    },
  };

  // 初始化时检查是否有传入的 location
  useEffect(() => {
    if (location?.pathname) {
      if (location.pathname.includes('/form')) {
        setCurrentView('form');
        setMockLocation(location);
      } else {
        setCurrentView('list');
      }
    }
  }, [location]);

  // 根据当前视图显示对应组件
  if (currentView === 'form') {
    return <ExceptionRecordsForm history={mockHistory} location={mockLocation} />;
  }

  // 默认显示列表页面
  return <ExceptionRecordsList history={mockHistory} />;
};

export default YTHLocalization.withLocal(
  ExceptionRecordsRoute,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
