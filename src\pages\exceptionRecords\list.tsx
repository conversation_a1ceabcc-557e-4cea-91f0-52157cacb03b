import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization, YTHDialog } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Button, message, Space } from 'antd';
import locales from '@/locales';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { DownloadOutlined } from '@ant-design/icons';
import ExceptionRecordsApi from '@/service/exceptionRecordsApi';
import {
  ExceptionRecordsVo,
  ExceptionRecordsPageQueryParam,
  ExceptionRecordsPageResponse,
  ExceptionRecordsQueryParam,
} from '@/types/exceptionRecords';
import baseApi from '@/service/baseApi';

/**
 * @description 异常记录管理 异常信息管理
 */
const ExceptionRecordsList: React.FC<{ history?: any }> = ({ history }) => {
  const ref: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();

  const [queryData, setQueryData] = useState<ExceptionRecordsPageQueryParam>(); // 查询参数 用于导出
  const [listDataTotal, setListDataTotal] = useState<number>(0);

  // 导出数据
  const exportByPage: () => Promise<void> = async () => {
    if (listDataTotal === 0) {
      message.warn('无可导出数据！');
      return;
    }
    if (listDataTotal > 10000) {
      message.warn('当前查询结果超过10000条，仅导出前10000条数据');
    }
    const params: ExceptionRecordsPageQueryParam = {
      ...queryData,
      pageSize: 10000,
      currentPage: 1,
    };
    try {
      await ExceptionRecordsApi.exportByPage(params);
    } catch (error) {
      message.error(error);
    }
  };

  // 跳转到新增页面
  const handleAdd = () => {
    if (history) {
      history.push('/exceptionRecords/form?mode=add');
    }
  };

  // 跳转到编辑页面
  const handleEdit = (record: ExceptionRecordsVo) => {
    if (history) {
      history.push(`/exceptionRecords/form?mode=edit&id=${record.id}`);
    }
  };

  // 跳转到查看页面
  const handleView = (record: ExceptionRecordsVo) => {
    if (history) {
      history.push(`/exceptionRecords/form?mode=view&id=${record.id}`);
    }
  };

  // 删除确认
  const confirmDelete: (row: ExceptionRecordsVo) => Promise<void> = async (row) => {
    const res: { code?: number; msg?: string } = await ExceptionRecordsApi.deleteById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      aa.reload({});
    } else {
      message.error('删除数据出错');
    }
  };

  // 删除弹窗
  const deleteTemplateDialog: (row: ExceptionRecordsVo) => Promise<void> = async (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  const columns: IYTHColumnProps[] = [
    { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
    { dataIndex: 'taskId', title: '任务ID', query: true, display: true },
    { dataIndex: 'taskPointId', title: '检查点ID', query: false, display: true },
    {
      dataIndex: 'exceptionType',
      title: '异常类型',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择异常类型',
        },
        request: async () => {
          return (await baseApi.getDictionary(dicParams.EXCEPTION_TYPE)) ?? [];
        },
      },
    },
    { dataIndex: 'exceptionTime', title: '异常发生时间', width: 150, query: false, display: true },
    { dataIndex: 'exceptionDescribe', title: '异常描述', width: 200, query: false, display: true },
    { dataIndex: 'reportUserName', title: '上报人', width: 100, query: false, display: true },
    {
      dataIndex: 'handleStatus',
      title: '处置状态',
      width: 100,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择处置状态',
        },
        request: async () => {
          return [
            { code: '0', text: '未处置' },
            { code: '1', text: '已处置' },
          ];
        },
      },
      render: (status: number) => (status === 1 ? '已处置' : '未处置'),
    },
    { dataIndex: 'handleUserName', title: '处置人', width: 100, query: false, display: true },
    { dataIndex: 'handleTime', title: '处置时间', width: 150, query: false, display: true },
    { dataIndex: 'createDate', title: '创建时间', width: 150, query: false, display: true },
    {
      dataIndex: 'startDate',
      title: '异常发生时间',
      width: 0,
      queryMode: 'group',
      display: false,
      query: true,
      componentName: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        precision: `day`,
        formatter: `YYYY-MM-DD`,
      },
    },
  ];

  // 获取异常类型的实际值（处理字符串或对象数组格式）
  const getExceptionTypeValue: (
    exceptionType:
      | string
      | { code?: string; text?: string; id?: string; value?: string; lable?: string }[]
      | undefined,
  ) => string = (exceptionType) => {
    if (typeof exceptionType === 'string') {
      return exceptionType;
    }
    if (Array.isArray(exceptionType) && exceptionType.length > 0) {
      return exceptionType[0].code || exceptionType[0].value || '';
    }
    return '';
  };

  // 获取处置状态的实际值（处理字符串或对象数组格式）
  const getHandleStatusValue: (
    handleStatus:
      | string
      | { code?: string; text?: string; id?: string; value?: string; lable?: string }[]
      | undefined,
  ) => string = (handleStatus) => {
    if (typeof handleStatus === 'string') {
      return handleStatus;
    }
    if (Array.isArray(handleStatus) && handleStatus.length > 0) {
      return handleStatus[0].code || handleStatus[0].value || '';
    }
    return '';
  };

  const handleFilter: (f: ExceptionRecordsQueryParam) => ExceptionRecordsQueryParam = (
    f: ExceptionRecordsQueryParam,
  ) => {
    const filter: ExceptionRecordsQueryParam = f || {};
    if (f.taskId && f.taskId !== '') {
      filter.taskId = f.taskId;
    }
    if (f.exceptionType && Array.isArray(f.exceptionType) && f.exceptionType.length > 0) {
      filter.exceptionType = f.exceptionType[0].code;
    }
    if (f.handleStatus && Array.isArray(f.handleStatus) && f.handleStatus.length > 0) {
      filter.handleStatus = f.handleStatus[0].code;
    }
    if (f.startDate_start && f.startDate_start !== '') {
      filter.startDate = f.startDate_start;
    }
    if (f.startDate_end && f.startDate_end !== '') {
      filter.endDate = f.startDate_end;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="exceptionRecordsList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        extraOperation={[
          {
            element: (
              <div>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  size="small"
                  onClick={exportByPage}
                >
                  导出
                </Button>
              </div>
            ),
          },
        ]}
        operation={[
          {
            element: (
              <div>
                <Button size="small" type="primary" onClick={handleAdd}>
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        request={async (filter, pagination, sort) => {
          try {
            const convertFieldName: (field: string) => string = (field: string) =>
              field.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();

            let descColumns: string = 'create_date';
            let ascColumns: string = '';

            if (sort?.order && sort.field) {
              const convertedField: string = convertFieldName(sort.field);
              if (sort.order === 'desc') {
                descColumns = convertedField;
              } else if (sort.order === 'asc') {
                ascColumns = convertedField;
              }
            }
            const queryParams: ExceptionRecordsPageQueryParam = {
              descs: [descColumns],
              aescs: [ascColumns],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            };
            setQueryData(queryParams);
            const resData: ExceptionRecordsPageResponse = await ExceptionRecordsApi.queryByPage(
              queryParams,
            );
            if (resData.code && resData.code === 200) {
              const dataWithSerialNo: (ExceptionRecordsVo & { serialNo: number })[] =
                resData.data.map((item: ExceptionRecordsVo, index: number) => ({
                  ...item,
                  serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
                }));
              setListDataTotal(resData.total);
              return {
                data: dataWithSerialNo,
                total: resData.total,
                success: true,
              };
            }
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        rowOperationWidth={200}
        rowProps={() => ({})}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div className="exception-records-row-operator">
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        handleView(row);
                      }}
                    >
                      查看
                    </Button>
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        handleEdit(row);
                      }}
                    >
                      编辑
                    </Button>
                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row as ExceptionRecordsVo);
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />
    </div>
  );
};

export default YTHLocalization.withLocal(
  ExceptionRecordsList,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
