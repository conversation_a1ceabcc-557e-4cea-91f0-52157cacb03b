import React, { useState, useEffect } from 'react';
import { YTHDatePicker, YTH<PERSON><PERSON>og, YTHF<PERSON>, YTHPickUser, YTHToast } from 'yth-ui';
import taskApi from '@/service/taskApi';
import {
  message,
  Button,
  Spin,
  Input,
  Tabs,
  Table,
  Space,
  Modal,
  Form,
  Select,
  DatePicker,
  Col,
  Row,
} from 'antd';
import { CurrentUser, Token } from '@/Constant';
import baseApi from '@/service/baseApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

import { BaseResponse } from '@/types/common';
import {
  TaskVo,
  TaskInsertParam,
  TaskUpdateParam,
  TaskDeviceVo,
  TaskPointsVo,
  TaskStartParam,
} from '@/types/task';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import moment from 'moment';
import { IUserProps } from 'yth-ui/es/components/pickUser';
import { Unit, User } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import RBAC from '@/service/RBAC';
import LineMap from '@/components/LocationWithDraw/view';
import style from './task.module.less';

const { TabPane } = Tabs;

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: TaskVo;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
  /** 弹窗是否可见 */
  visible: boolean;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const AddDialog: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {}, visible }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);
  const [taskDevicesList, setTaskDevicesList] = useState<TaskDeviceVo[]>([]);
  // 全部设备列表
  const [devicesList, setDevicesList] = useState<Array<{ label: string; value: string }>>([]);
  // 巡检线路列表
  const [inspectionLineList, setInspectionLineList] = useState<InspectionTaskLineVo[]>([]);
  const [taskPointsList, setTaskPointsList] = useState<TaskPointsVo[]>([]);
  const [pointModalVisible, setPointModalVisible] = useState<boolean>(false);
  const [currentPoint, setCurrentPoint] = useState<TaskPointsVo>({});
  const [editingPointIndex, setEditingPointIndex] = useState<number>(-1);
  const [currentInspectionMethod, setCurrentInspectionMethod] = useState<string>('');
  const [activeTabKey, setActiveTabKey] = useState<string>('1');
  const [taskTypeOptions, setTaskTypeOptions] = useState<Array<{ label: string; value: string }>>(
    [],
  );
  // 检查点状态 1 已检查 0 未检查
  const [currentPointCheckStatus, setCurrentPointCheckStatus] = useState<number>(1);
  // 检查结果 1 正常 0 异常
  const [currentPointCheckResult, setCurrentPointCheckResult] = useState<number>(1);
  // 异常是否需要处置 1 是 0 否
  const [currentPointIsNeedHandle, setCurrentPointIsNeedHandle] = useState<number>(0);
  // 检查点弹窗类型
  const [pointModalType, setPointModalType] = useState<string>('add');
  const [inspectionMethodOptions, setInspectionMethodOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);
  const [taskStatusOptions, setTaskStatusOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);

  // 获取任务状态的实际值（处理字符串或对象数组格式）
  const getTaskStatusValue: (
    taskStatus:
      | string
      | { code?: string; text?: string; id?: string; value?: string; lable?: string }[]
      | undefined,
  ) => string = (taskStatus) => {
    if (typeof taskStatus === 'string') {
      return taskStatus;
    }
    if (Array.isArray(taskStatus) && taskStatus.length > 0) {
      return taskStatus[0].code || taskStatus[0].value || '';
    }
    return '';
  };

  // 表单
  const [form] = Form.useForm();
  const pointForm: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  const { TextArea } = Input;

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    try {
      const res: BaseResponse<TaskVo> = await taskApi.getDetailById({
        id: dataObj.id,
      });
      if (res && res.code && res.code === 200) {
        const formD: TaskVo = res.data;

        // 处理 时间 组件的数据格式
        const processedFormD: TaskVo = { ...formD };
        if (formD.planEndTime) {
          processedFormD.planEndTime = moment(formD.planEndTime);
        }
        if (formD.planStartTime) {
          processedFormD.planStartTime = moment(formD.planStartTime);
        }
        if (formD.actualEndTime) {
          processedFormD.actualEndTime = moment(formD.actualEndTime);
        }
        if (formD.actualStartTime) {
          processedFormD.actualStartTime = moment(formD.actualStartTime);
        }
        if (formD.taskDevicesIds) {
          processedFormD.taskDevicesIds = Array.isArray(formD.taskDevicesIds)
            ? formD.taskDevicesIds
            : formD.taskDevicesIds.split(',');
        } else {
          processedFormD.taskDevicesIds = [];
        }
        form.setFieldsValue(processedFormD);
        setTaskDevicesList(Array.isArray(formD.taskDevicesList) ? formD.taskDevicesList : []);
        setTaskPointsList(Array.isArray(formD.taskPointsList) ? formD.taskPointsList : []);

        // 设置当前巡检方式
        if (typeof formD.inspectionMethod === 'string' && formD.inspectionMethod) {
          setCurrentInspectionMethod(formD.inspectionMethod);
        }
        setInspectionLineList(formD.inspectionLineList || []);
      } else {
        message.error('获取任务详情失败');
        // 即使获取失败，也要确保列表为空数组
        setTaskDevicesList([]);
        setTaskPointsList([]);
        setInspectionLineList([]);
      }
    } catch {
      message.error('获取任务详情失败');
      // 确保列表为空数组
      setTaskDevicesList([]);
      setTaskPointsList([]);
      setInspectionLineList([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (
      type &&
      (type === 'edit' || type === 'view' || type === 'startTask' || type === 'endTask')
    ) {
      queryDataDetail()
        .then(() => {
          setIsCasRequested(true);
        })
        .catch(() => {
          // 即使查询失败也要显示表单
          setIsCasRequested(true);
        });
    } else {
      // 新增模式下确保列表为空数组
      setTaskDevicesList([]);
      setTaskPointsList([]);
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 加载字典数据 、设备列表
  useEffect(() => {
    const loadDictionaries: () => Promise<void> = async () => {
      try {
        const [taskTypes, inspectionMethods, taskStatuses, devices] = await Promise.all([
          baseApi.getDictionary(dicParams.PLAN_TYPE),
          baseApi.getDictionary(dicParams.INSPECTION_METHOD),
          baseApi.getDictionary(dicParams.TASK_STATUS),
          baseApi.getEquipmentList(dicParams.EQUIPMENT_CATEGORY),
        ]);

        setTaskTypeOptions(taskTypes.map((item) => ({ label: item.text, value: item.code })));
        setInspectionMethodOptions(
          inspectionMethods.map((item) => ({ label: item.text, value: item.code })),
        );
        setTaskStatusOptions(taskStatuses.map((item) => ({ label: item.text, value: item.code })));
        setDevicesList(devices.map((item) => ({ label: item.name, value: item.uniqueCode })));
      } catch (error) {
        console.error('加载字典数据失败:', error);
      }
    };

    loadDictionaries();
  }, []);
  // 处理巡检方式变化
  const handleInspectionMethodChange: (value: unknown) => void = (value: unknown): void => {
    let methodCode: string = '';
    if (Array.isArray(value)) {
      methodCode = value[0]?.code || '';
    } else if (typeof value === 'string') {
      methodCode = value;
    }
    console.log('methodCode', methodCode);
    setCurrentInspectionMethod(methodCode);
    form.setFieldsValue({ inspectionMethod: methodCode });
  };

  // 设置弹窗标题
  const setModalTitle: () => string = (): string => {
    let title: string = '';
    if (type === 'add') {
      title = '新增';
    } else if (type === 'view') {
      title = '查看';
    } else if (type === 'edit') {
      title = '编辑';
    } else if (type === 'startTask') {
      title = '开始任务';
    } else if (type === 'endTask') {
      title = '完成任务';
    }
    return title;
  };

  // 处理提交数据的公共方法
  const processSubmitData: (
    data: TaskInsertParam | TaskUpdateParam,
  ) => TaskInsertParam | TaskUpdateParam = (
    data: TaskInsertParam | TaskUpdateParam,
  ): TaskInsertParam | TaskUpdateParam => {
    const submitData: TaskInsertParam | TaskUpdateParam = {
      ...data,
      taskDevicesList,
      taskPointsList,
    };

    // 处理巡检方式字段
    if (data.inspectionMethod) {
      if (Array.isArray(data.inspectionMethod)) {
        submitData.inspectionMethod = data.inspectionMethod[0]?.code || '';
      } else {
        submitData.inspectionMethod = data.inspectionMethod;
      }
    } else {
      submitData.inspectionMethod = '';
    }

    // 处理任务类型字段
    if (data.taskType) {
      if (Array.isArray(data.taskType)) {
        submitData.taskType = data.taskType[0]?.code || '';
      } else {
        submitData.taskType = data.taskType;
      }
    } else {
      submitData.taskType = '';
    }

    // 处理任务状态字段
    if (data.taskStatus) {
      if (Array.isArray(data.taskStatus)) {
        submitData.taskStatus = data.taskStatus[0]?.code || '';
      } else {
        submitData.taskStatus = data.taskStatus;
      }
    } else {
      submitData.taskStatus = '';
    }

    // 处理是否提醒字段
    if (typeof data.isRemind !== 'undefined') {
      if (Array.isArray(data.isRemind)) {
        submitData.isRemind = data.isRemind[0]?.code || null;
      } else {
        submitData.isRemind = data.isRemind;
      }
    }

    // 处理 时间 组件的数据格式
    if (data.planEndTime) {
      submitData.planEndTime = moment(data.planEndTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.planStartTime) {
      submitData.planStartTime = moment(data.planStartTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.actualEndTime) {
      submitData.actualEndTime = moment(data.actualEndTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.actualStartTime) {
      submitData.actualStartTime = moment(data.actualStartTime).format('YYYY-MM-DD HH:mm:ss');
    }
    // 处理设备id列表
    if (Array.isArray(data.taskDevicesIds)) {
      submitData.taskDevicesIds = data.taskDevicesIds.join(',');
    }

    return submitData;
  };

  // 点击取消
  const cancel: () => void = () => {
    form.resetFields();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: TaskInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const submitData: TaskInsertParam = processSubmitData(data) as TaskInsertParam;
    const res: BaseResponse<object> = await taskApi.insert(submitData);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: TaskUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const processedData: TaskUpdateParam = processSubmitData(data) as TaskUpdateParam;
    const submitData: TaskUpdateParam = {
      ...processedData,
      id: dataObj?.id,
    };
    const res: BaseResponse<object> = await taskApi.update(submitData);
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    try {
      const values: TaskInsertParam | TaskUpdateParam = await form.validateFields();
      const submitData: TaskInsertParam = JSON.parse(JSON.stringify(values));
      if (type === 'add') {
        submitAddData(submitData);
        return;
      }
      submitEditData(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 开始任务
  const satrtTask: () => Promise<void> = async () => {
    try {
      const values: TaskUpdateParam = await form.validateFields();
      const processedData: TaskUpdateParam = processSubmitData(values) as TaskUpdateParam;
      const submitData: TaskStartParam = {
        ...processedData,
        taskId: values.id,
      };
      if (dicParams.INSPECTION_METHOD_HELMET === submitData.inspectionMethod) {
        if (!submitData.taskDevicesIds) {
          message.error('请选择巡检设备');
          return;
        }
      }
      setIsLoading(true);
      const res: BaseResponse<object> = await taskApi.startTask(submitData);
      if (res && res.code && res.code === 200) {
        message.success('开始任务成功');
        closeModal();
      } else {
        message.error('开始任务失败');
      }
      setIsLoading(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 结束任务
  const confirmFinishTask: (taskData: TaskUpdateParam) => Promise<void> = async (taskData) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await taskApi.finishTask(taskData);
    if (res && res.code && res.code === 200) {
      message.success('结束任务成功');
      closeModal();
    } else {
      message.error('结束任务失败');
    }
    setIsLoading(false);
  };

  // 完成任务
  const finishTask: () => Promise<void> = async () => {
    try {
      const values: TaskInsertParam | TaskUpdateParam = await form.validateFields();
      const submitData: TaskInsertParam = JSON.parse(JSON.stringify(values));
      const processedData: TaskUpdateParam = processSubmitData(submitData) as TaskUpdateParam;
      const pointsList: TaskPointsVo[] = processedData.taskPointsList;
      // 是否已完成检查点检查
      let isAllChecked: boolean = true;
      if (pointsList && pointsList.length > 0) {
        pointsList.forEach((item) => {
          if (typeof item.checkStatus === 'undefined' || item.checkStatus === 0) {
            isAllChecked = false;
          }
        });
      }
      if (!isAllChecked) {
        message.error('请先完成各检查点检查！');
        return;
      }
      YTHDialog.show({
        type: 'confirm',
        content: <p>完成任务后数据不可修改，确认完成此任务？</p>,
        onCancle: () => {},
        onConfirm: () => {
          confirmFinishTask(processedData);
        },
        p_props: {
          cancelText: '取消',
          okText: '确定',
          title: '删除',
        },
        m_props: {
          title: '删除',
        },
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  /**
   * 新增检查点
   */
  const handleAddPoint: () => void = () => {
    setPointModalType('add');
    // 检查状态
    setCurrentPointCheckStatus(0);
    // 检查结果 1 正常 0 异常
    setCurrentPointCheckResult(1);
    // 异常是否需要处置 1 是 0 否
    setCurrentPointIsNeedHandle(0);
    setCurrentPoint({});
    setEditingPointIndex(-1);
    pointForm.reset();
    setPointModalVisible(true);
  };
  /**
   * 编辑检查点
   */
  const handleEditPoint: (record: TaskPointsVo, index: number, editOrView: string) => void = (
    record: TaskPointsVo,
    index: number,
    editOrView: string,
  ) => {
    setPointModalType(editOrView);
    const newRecord: TaskPointsVo = {
      ...record,
      handleUserName: [{ id: record?.handleUserId, name: record?.handleUserIdText || '' }],
    };
    setCurrentPointCheckStatus(0);
    setCurrentPointCheckResult(1);
    setCurrentPointIsNeedHandle(0);
    // 检查状态 0 未检查 1 已检查
    if (typeof record.checkStatus === 'number') {
      // 检查状态
      setCurrentPointCheckStatus(record.checkStatus);
      newRecord.checkStatus = [
        { code: record?.checkStatus, value: record?.checkStatus, text: `${record?.checkStatus}` },
      ];
    }
    // 检查结果 1 正常 0 异常
    if (typeof record.checkResult === 'number') {
      setCurrentPointCheckResult(record.checkResult as number);
      newRecord.checkResult = [
        { code: record?.checkResult, value: record?.checkResult, text: `${record?.checkResult}` },
      ];
    }
    // 异常是否需要处置 1 是 0 否
    if (typeof record.isNeedHandle === 'number') {
      setCurrentPointIsNeedHandle(record.isNeedHandle);
      newRecord.isNeedHandle = [
        {
          code: record?.isNeedHandle,
          value: record?.isNeedHandle,
          text: `${record?.isNeedHandle}`,
        },
      ];
    }
    // 处理附件
    newRecord.attachments = newRecord?.attachments ? JSON.parse(newRecord.attachments) : [];
    newRecord.isNeedHandle = newRecord.isNeedHandle || [];
    newRecord.checkResult = newRecord.checkResult || [];
    newRecord.isNeedHandle = newRecord.isNeedHandle || [];

    console.log('newRecord', newRecord);
    setCurrentPoint(newRecord);
    setEditingPointIndex(index);
    pointForm.setValues(newRecord);
    setPointModalVisible(true);
  };

  /**
   * 关闭检查点弹窗
   */
  const closePointModal: () => void = () => {
    pointForm.reset();
    setPointModalVisible(false);
  };
  /**
   * 删除检查点
   */
  const handleDeletePoint: (id: string, index: number) => void = async (
    id: string,
    index: number,
  ) => {
    setIsLoading(true);
    const addRs: BaseResponse<object> = await taskApi.deleteTaskPointById(id);
    if (addRs && addRs.code && addRs.code === 200) {
      message.success('删除检查点成功');
      closePointModal();
      const newList: TaskPointsVo[] = [...taskPointsList];
      newList.splice(index, 1);
      setTaskPointsList(newList);
    } else {
      message.error('删除检查点失败');
    }
    setIsLoading(false);
  };

  // 校验失败提示
  const showFormRequiredFail: () => void = () => {
    YTHToast.show({
      type: 'error',
      messageText: '表格信息不正确,请按照提示填写',
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
  };

  // 确定新增
  const handlePointOk: (checkStatus: number) => void = async (checkStatus: number) => {
    const res: string = await pointForm.validate();
    if (res !== 'fail') {
      const pointData: TaskPointsVo = JSON.parse(JSON.stringify(pointForm.values));
      pointData.checkStatus = checkStatus;
      pointData.taskId = dataObj.id;
      // 检查结果 1 正常 0 异常
      if (Array.isArray(pointData.checkResult)) {
        pointData.checkResult = pointData?.checkResult?.[0]?.code as number;
      }
      // 异常是否需要处置 1 是 0 否
      if (Array.isArray(pointData.isNeedHandle)) {
        pointData.isNeedHandle = pointData?.isNeedHandle?.[0]?.code as number;
      }
      // 处置人
      if (Array.isArray(pointData.handleUserName)) {
        pointData.handleUserId = pointData?.handleUserName?.[0]?.id;
      }
      // 处理附件
      if (pointData.attachments) {
        pointData.attachments = JSON.stringify(pointData.attachments);
      }
      // 检查结果 1 正常 0 异常
      if (currentPointCheckResult === 0) {
        if (typeof pointData.isNeedHandle === 'undefined') {
          message.error('请确认异常是否需要进行处置');
          return;
        }
        if (currentPointIsNeedHandle === 1) {
          if (!pointData.handleUserId) {
            message.error('请选择异常处置人');
            return;
          }
          if (!pointData.handleRequire) {
            message.error('请输入异常处置要求');
            return;
          }
        }
      }
      if (editingPointIndex >= 0) {
        // 编辑
        setIsLoading(true);
        const addRs: BaseResponse<object> = await taskApi.updateTaskPoint(pointData);
        if (addRs && addRs.code && addRs.code === 200) {
          message.success('编辑检查点成功');
          closePointModal();
          const newList: TaskPointsVo[] = [...taskPointsList];
          newList[editingPointIndex] = addRs.data;
          setTaskPointsList(newList);
        } else {
          message.error('编辑检查点失败');
        }
        setIsLoading(false);
      } else {
        // 新增
        setIsLoading(true);
        const addRs: BaseResponse<object> = await taskApi.insertTaskPoint(pointData);
        if (addRs && addRs.code && addRs.code === 200) {
          message.success('新增检查点成功');
          closePointModal();
          setTaskPointsList([...taskPointsList, addRs.data]);
        } else {
          message.error('新增检查点失败');
        }
        setIsLoading(false);
      }
      pointForm.reset();
      setPointModalVisible(false);
    } else {
      showFormRequiredFail();
      setIsLoading(false);
    }
  };

  // 处理检查结果变化
  const handleCheckResultChange: (value: unknown) => void = (value: unknown): void => {
    let checkResult: number = 0;
    if (Array.isArray(value)) {
      checkResult = Number(value[0]?.code);
    } else if (typeof value === 'number') {
      checkResult = value;
    }
    setCurrentPointCheckResult(checkResult);
  };
  // 处理异常是否需要处置变化
  const handleIsNeedHandleChange: (value: unknown) => void = (value: unknown): void => {
    let isNeedHandle: number = 0;
    if (Array.isArray(value)) {
      isNeedHandle = Number(value[0]?.code);
    } else if (typeof value === 'number') {
      isNeedHandle = value;
    }
    setCurrentPointIsNeedHandle(isNeedHandle);
  };

  // 检查点表格列定义
  const pointColumns: IYTHColumnProps[] = [
    {
      title: '检查点名称',
      dataIndex: 'pointName',
      key: 'pointName',
    },
    {
      title: '检查点坐标',
      dataIndex: 'pointLocation',
      key: 'pointLocation',
    },
    {
      title: '巡检内容',
      dataIndex: 'inspectionContent',
      key: 'inspectionContent',
    },
    {
      title: '检查状态',
      dataIndex: 'checkStatus',
      key: 'checkStatus',
      render: (status: number) => (status === 1 ? '已检查' : '未检查'),
    },
    {
      title: '检查结果',
      dataIndex: 'checkResult',
      key: 'checkResult',
      render: (result: number) => (result === 1 ? '正常' : '异常'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: TaskPointsVo, index: number) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              handleEditPoint(record, index, 'view');
            }}
          >
            查看
          </Button>
          {(type !== 'view' || dicParams.UAV === dataObj.inspectionMethod) &&
            (typeof record.checkStatus === 'undefined' || record.checkStatus === 0) && (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEditPoint(record, index, 'edit')}
              >
                编辑
              </Button>
            )}
          {(type !== 'view' || dicParams.UAV === dataObj.inspectionMethod) &&
            (typeof record.checkStatus === 'undefined' || record.checkStatus === 0) &&
            dataObj.inspectionMethod === dicParams.UAV && (
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeletePoint(record.id, index)}
              >
                删除
              </Button>
            )}
        </Space>
      ),
      dataIndex: '',
    },
  ];

  return (
    <Modal
      width="80%"
      className={style['add-task-moduel']}
      title={setModalTitle()}
      style={{ top: 30 }}
      visible={visible}
      onCancel={closeModal}
      destroyOnClose
      maskClosable={false}
      footer={[
        // dicParams.TASK_STATUS_DOING === getTaskStatusValue(dataObj.taskStatus as string) &&
        //   dicParams.UAV !== dataObj.inspectionMethod && (
        //     <Button onClick={save} className={style['search-btn']} type="primary">
        //       保存
        //     </Button>
        //   ),
        dicParams.TASK_STATUS_WAIT === getTaskStatusValue(dataObj.taskStatus as string) &&
          dicParams.UAV !== dataObj.inspectionMethod &&
          type !== 'view' && (
            <Button onClick={satrtTask} className={style['search-btn']} type="primary">
              开始任务
            </Button>
          ),
        dicParams.TASK_STATUS_DOING === getTaskStatusValue(dataObj.taskStatus as string) &&
          dicParams.UAV !== dataObj.inspectionMethod && (
            <Button onClick={finishTask} className={style['search-btn']} type="primary">
              完成任务
            </Button>
          ),

        <Button key="cancel" onClick={cancel} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <Spin spinning={isLoading}>
        <div className={style['yth-inspection-moduel']}>
          {isCasRequested && (
            <div>
              <div className={style['task-form-title']}>巡检任务信息</div>
              <Form
                form={form}
                size="small"
                colon={false}
                labelCol={{ span: 6 }}
                className={style['task-form']}
              >
                <Form.Item name="id" label="id" hidden>
                  <Input disabled />
                </Form.Item>
                <Row>
                  <Col span={12}>
                    <Form.Item name="taskCode" label="任务编码">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="taskName"
                      label="任务名称"
                      rules={[{ required: true, message: '请输入任务名称' }]}
                    >
                      <Input disabled />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <Form.Item name="taskType" label="任务类型">
                      <Select disabled options={taskTypeOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="planName" label="所属计划名称">
                      <Input disabled />
                    </Form.Item>
                    <Form.Item hidden name="planId" label="所属计划">
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <Form.Item name="inspectionMethod" label="巡检方式">
                      <Select
                        disabled
                        options={inspectionMethodOptions}
                        onChange={handleInspectionMethodChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="directorUserName" label="负责人">
                      <Input disabled />
                    </Form.Item>
                    {/* <YTHForm.Item
                  name="planPoints"
                  title="巡检路线"
                  labelType={2}
                  required
                  componentName="Input"
                  componentProps={{
                    disabled: type === 'view',
                    placeholder: '请输入负责人',
                  }}
                /> */}
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item name="partyUserNames" labelCol={{ span: 3 }} label="参与巡检人">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row>
                  <Col span={3} className={style['lable-col']}>
                    <Form.Item
                      label="计划开始时间"
                      colon={false}
                      className={style['lable-form-item']}
                      labelCol={{ span: 24 }}
                    />
                  </Col>
                  <Col span={9}>
                    <Row>
                      <Col span={11} className={style['start-time-item']}>
                        <Form.Item name="planStartTime" label="计划开始时间" labelCol={{ span: 0 }}>
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="计划开始时间"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={13} className={style['lable-col']}>
                        <Form.Item
                          name="planEndTime"
                          label="至"
                          labelCol={{ span: 5 }}
                          className={style['center-label-form-item']}
                        >
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="计划结束时间"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>
                  <Col span={3} className={style['lable-col']}>
                    <Form.Item
                      label="实际开始时间"
                      colon={false}
                      className={style['lable-form-item']}
                      labelCol={{ span: 24 }}
                    />
                  </Col>
                  <Col span={9}>
                    <Row>
                      <Col span={11}>
                        <Form.Item
                          name="actualStartTime"
                          label="实际开始时间"
                          labelCol={{ span: 0 }}
                        >
                          <DatePicker
                            showTime
                            placeholder="实际开始时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={13} className={style['lable-col']}>
                        <Form.Item
                          name="actualEndTime"
                          label="至"
                          className={style['center-label-form-item']}
                          labelCol={{ span: 5 }}
                        >
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            disabled
                            placeholder="实际结束时间"
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>
                </Row>
                <Row style={{ overflow: 'visible' }}>
                  <Col span={12} className={style['lable-col']}>
                    <Form.Item name="taskDevicesIds" label="巡检设备" labelCol={{ span: 6 }}>
                      <Select
                        mode="multiple"
                        className={
                          style[
                            type === 'startTask' &&
                            dicParams.INSPECTION_METHOD_HELMET === currentInspectionMethod
                              ? 'required-item'
                              : ''
                          ]
                        }
                        disabled={
                          !(
                            type === 'startTask' &&
                            dicParams.INSPECTION_METHOD_HELMET === currentInspectionMethod
                          )
                        }
                        placeholder="请选择巡检设备"
                        options={devicesList.map((device) => ({
                          label: `${device.label}`,
                          value: device.value,
                        }))}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="taskStatus"
                      label="任务状态"
                      labelCol={{ span: 6 }}
                      wrapperCol={{ span: 18 }}
                    >
                      <Select disabled options={taskStatusOptions} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row style={{ height: 'auto', borderTop: '2px solid #e4e4e4' }}>
                  <Col span={24}>
                    <Form.Item name="remark" label="备注" labelCol={{ span: 3 }}>
                      <TextArea
                        className={
                          style[
                            type === 'startTask' &&
                            dicParams.INSPECTION_METHOD_HELMET === currentInspectionMethod
                              ? 'required-item'
                              : ''
                          ]
                        }
                        disabled={
                          !(
                            type === 'startTask' &&
                            dicParams.INSPECTION_METHOD_HELMET === currentInspectionMethod
                          )
                        }
                        placeholder="请输入备注信息"
                        rows={3}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row style={{ height: '1px' }}>
                  <Col span={24}>
                    <Form.Item name="isRemind" label="是否发送短信提醒" hidden>
                      <Select
                        disabled={type === 'view'}
                        placeholder="请选择是否发送短信提醒"
                        options={[
                          { label: '不发送', value: 0 },
                          { label: '发送', value: 1 },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>

              <div style={{ marginTop: 20 }}>
                <Tabs
                  defaultActiveKey="1"
                  activeKey={activeTabKey}
                  onChange={setActiveTabKey}
                  tabBarExtraContent={
                    activeTabKey === '1' &&
                    dataObj.id &&
                    currentInspectionMethod === dicParams.UAV && (
                      <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPoint}>
                        添加检查点
                      </Button>
                    )
                  }
                >
                  <TabPane tab="任务检查点" key="1">
                    <Table
                      columns={pointColumns}
                      dataSource={taskPointsList}
                      rowKey={(record, index) => index?.toString() || '0'}
                      pagination={false}
                      size="small"
                    />
                  </TabPane>
                  <TabPane tab="巡检线路" key="2">
                    <LineMap inspectionLineList={inspectionLineList || []} />
                  </TabPane>
                </Tabs>
              </div>
            </div>
          )}

          {/* 检查点编辑弹窗 */}
          <Modal
            width="900px"
            title={editingPointIndex >= 0 ? '编辑检查点' : '添加检查点'}
            visible={pointModalVisible}
            okText="保存"
            cancelText="取消"
            onCancel={() => {
              pointForm.reset();
              setPointModalVisible(false);
            }}
            destroyOnClose
            footer={[
              // 未检查
              pointModalType !== 'view' && currentPointCheckStatus === 0 && (
                <Button
                  onClick={() => handlePointOk(0)}
                  className={style['search-btn']}
                  type="primary"
                >
                  保存
                </Button>
              ),

              pointModalType !== 'view' && currentPointCheckStatus === 0 && (
                <Button
                  onClick={() => handlePointOk(1)}
                  className={style['search-btn']}
                  type="primary"
                >
                  保存并提交
                </Button>
              ),
              <Button key="cancel" onClick={closePointModal} className={style['reset-btn']}>
                取消
              </Button>,
            ]}
          >
            <YTHForm form={pointForm} col={2}>
              <YTHForm.Item
                name="pointName"
                title="检查点名称"
                labelType={2}
                required
                disabled={pointModalType === 'view' || dicParams.UAV !== dataObj.inspectionMethod}
                componentName="Input"
                componentProps={{
                  placeholder: '请输入检查点名称',
                }}
              />
              <YTHForm.Item
                name="pointLocation"
                title="检查点坐标"
                labelType={2}
                required
                disabled={pointModalType === 'view'}
                componentName="Input"
                componentProps={{
                  placeholder: '请输入检查点坐标',
                }}
              />
              <YTHForm.Item
                name="inspectionContent"
                title="巡检内容"
                labelType={2}
                required
                disabled={pointModalType === 'view' || dicParams.UAV !== dataObj.inspectionMethod}
                component={TextArea}
                componentProps={{
                  placeholder: '请输入巡检内容',
                  rows: 3,
                }}
                mergeRow={2}
              />
              {/* <YTHForm.Item
                name="checkStatus"
                title="检查状态"
                labelType={2}
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return [
                      { code: '0', text: '未检查' },
                      { code: '1', text: '已检查' },
                    ];
                  },
                  p_props: {
                    placeholder: '请选择检查状态',
                  },
                }}
              /> */}
              <YTHForm.Item
                name="checkResult"
                title="检查结果"
                required
                disabled={pointModalType === 'view'}
                labelType={2}
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return [
                      { code: 1, text: '正常' },
                      { code: 0, text: '异常' },
                    ];
                  },
                  p_props: {
                    placeholder: '请选择检查结果',
                  },
                  onChange: (value: number) => {
                    handleCheckResultChange(value);
                  },
                }}
              />
              {currentPointCheckResult === 0 && (
                <>
                  <YTHForm.Item
                    name="exceptionDescribe"
                    title="异常描述"
                    labelType={2}
                    required
                    disabled={pointModalType === 'view'}
                    component={TextArea}
                    componentProps={{
                      placeholder: '请输入异常描述',
                      rows: 2,
                    }}
                    mergeRow={2}
                  />
                  <YTHForm.Item
                    name="isNeedHandle"
                    title="异常是否需要处置"
                    labelType={2}
                    componentName="Selector"
                    disabled={pointModalType === 'view'}
                    componentProps={{
                      request: async () => {
                        return [
                          { code: 0, text: '不需要处置' },
                          { code: 1, text: '需要处置' },
                        ];
                      },
                      p_props: {
                        placeholder: '请选择异常是否需要处置',
                      },
                      onChange: (value: number) => {
                        handleIsNeedHandleChange(value);
                      },
                    }}
                  />
                </>
              )}
              {currentPointIsNeedHandle === 1 && (
                <>
                  <YTHForm.Item
                    name="handleUserName"
                    title="处置人"
                    labelType={2}
                    disabled={pointModalType === 'view'}
                    componentName="PickUser"
                    componentProps={{
                      defaultOrganize: {
                        id: CurrentUser()?.unitId,
                        name: '',
                        type: 'org',
                      },
                      requestOrganize: async () => {
                        const resData: Unit = await baseApi.getUnitTree();
                        return formatTree(resData, 'unitType', 'unitName');
                      },
                      requestUser: async (organize: Unit) => {
                        const resData: User[] = await baseApi.getUserList(organize.id);
                        const newUsers: User[] = resData?.map((item) => {
                          return { ...item, name: item.realName, type: 'user' };
                        });
                        return newUsers;
                      },
                      multiple: false,
                    }}
                  />
                  <YTHForm.Item
                    name="handleDeadlineTime"
                    title="处置期限"
                    component={YTHDatePicker}
                    disabled={pointModalType === 'view'}
                    componentProps={{
                      placeholder: '请输入',
                      range: false,
                      formatter: `YYYY-MM-DD HH:mm:ss`,
                      precision: 'second',
                      p_props: {
                        placeholder: ['处置期限'],
                      },
                    }}
                  />
                  <YTHForm.Item
                    name="handleRequire"
                    title="处置要求"
                    component={TextArea}
                    disabled={pointModalType === 'view'}
                    mergeRow={1}
                    required={false}
                    componentProps={{
                      placeholder: '请输入处置要求',
                    }}
                  />
                </>
              )}

              <YTHForm.Item
                name="attachments"
                title="结果照片"
                labelType={2}
                componentName="Upload"
                disabled={pointModalType === 'view'}
                componentProps={{
                  listType: `yth-card`,
                  name: 'file',
                  action: '/gw/form-api/file/upload',
                  headers: {
                    authorization: Token(),
                  },
                  online: '/preview/onlinePreview',
                  data: {
                    formCode: 'task_attachments',
                  },
                }}
                mergeRow={2}
              />
              <YTHForm.Item
                name="checkResultDescription"
                title="备注"
                labelType={2}
                component={TextArea}
                disabled={pointModalType === 'view'}
                componentProps={{
                  placeholder: '请输入备注',
                  rows: 3,
                }}
                mergeRow={2}
              />
            </YTHForm>
          </Modal>
        </div>
      </Spin>
    </Modal>
  );
};

export default AddDialog;
