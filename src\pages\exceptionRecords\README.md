# 异常记录管理模块

## 概述

异常记录管理模块提供了完整的异常信息增删改查功能，支持独立页面的新增、编辑和查看操作。

## 功能特性

### 列表功能
- ✅ 分页查询异常记录
- ✅ 按异常类型筛选
- ✅ 按处置状态筛选
- ✅ 按时间范围筛选
- ✅ 数据导出功能
- ✅ 删除确认操作

### 表单功能
- ✅ 新增异常记录（独立页面）
- ✅ 编辑异常记录（独立页面）
- ✅ 查看异常记录详情（独立页面）
- ✅ 表单验证
- ✅ 时间选择器
- ✅ 下拉选择器

## 文件结构

```
src/pages/exceptionRecords/
├── index.tsx                    # 路由组件，管理页面跳转
├── list.tsx                     # 列表页面
├── form.tsx                     # 表单页面（新增/编辑/查看）
├── test.tsx                     # 测试组件
├── exceptionRecords.module.less # 样式文件
└── README.md                    # 说明文档
```

## 页面路由

### 列表页面
- **路径**: `/exceptionRecords/list`
- **功能**: 显示异常记录列表，支持查询、导出、新增、编辑、删除操作

### 表单页面
- **新增**: `/exceptionRecords/form?mode=add`
- **编辑**: `/exceptionRecords/form?mode=edit&id={recordId}`
- **查看**: `/exceptionRecords/form?mode=view&id={recordId}`

## 使用方式

### 1. 在 build.json 中配置模块

```json
{
  "modules": {
    "exceptionRecords": "./src/pages/exceptionRecords/index.tsx"
  }
}
```

### 2. 在页面中使用

```tsx
import ExceptionRecords from '@/pages/exceptionRecords';

// 基本使用
<ExceptionRecords history={history} location={location} />

// 或者直接使用测试组件
import ExceptionRecordsTest from '@/pages/exceptionRecords/test';
<ExceptionRecordsTest />
```

## API 接口

模块使用 `src/service/exceptionRecordsApi.ts` 中的接口：

- `insert(data)` - 新增异常记录
- `update(data)` - 更新异常记录  
- `deleteById(id)` - 删除异常记录
- `getDetailById(data)` - 获取异常记录详情
- `queryByPage(data)` - 分页查询异常记录
- `exportByPage(data)` - 导出异常记录

## 数据字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskId | string | ✅ | 任务ID |
| taskPointId | string | ❌ | 检查点ID |
| exceptionType | string | ✅ | 异常类型 |
| exceptionTime | Date | ✅ | 异常发生时间 |
| exceptionDescribe | string | ✅ | 异常描述 |
| reportUserId | string | ❌ | 上报人ID |
| reportUserName | string | ❌ | 上报人姓名 |
| handleStatus | number | ❌ | 处置状态（0:未处置, 1:已处置） |
| handleUserId | string | ❌ | 处置人ID |
| handleUserName | string | ❌ | 处置人姓名 |
| handleTime | Date | ❌ | 处置时间 |
| handleMethod | string | ❌ | 处置方式 |
| handleFiles | string | ❌ | 处置附件 |

## 样式定制

模块使用 Less 模块化样式，主要样式类：

- `.exception-records-form` - 表单容器
- `.exception-records-row-operator` - 行操作按钮
- `.required-item` - 必填项样式
- `.search-btn` - 搜索按钮样式
- `.reset-btn` - 重置按钮样式

## 响应式设计

模块支持响应式设计，在移动端（768px以下）会自动调整布局：

- 表单字段变为单列布局
- 操作按钮变为全宽度
- 卡片头部变为垂直布局

## 国际化支持

模块使用 `YTHLocalization` 进行国际化处理，支持多语言切换。

## 注意事项

1. **独立页面设计**: 新增和编辑功能使用独立页面，而非弹窗形式
2. **路由管理**: 使用内置的路由管理器处理页面跳转
3. **表单验证**: 所有必填字段都有完整的验证规则
4. **时间处理**: 使用 moment.js 处理时间格式转换
5. **字典数据**: 异常类型等下拉选项从字典接口获取

## 开发调试

### 本地测试
```bash
npm start
# 访问 http://localhost:30352/exceptionRecordsTest.js
```

### 构建发布
```bash
npm run build
```

## 扩展开发

如需扩展功能，可以：

1. 在 `form.tsx` 中添加新的表单字段
2. 在 `list.tsx` 中添加新的列表列
3. 在 `exceptionRecords.module.less` 中添加自定义样式
4. 在 `src/types/exceptionRecords.ts` 中扩展类型定义

## 技术栈

- React 17+
- TypeScript
- Ant Design 4.19.5
- YTH-UI 1.0.75
- Moment.js 2.29.3
- Less (CSS 预处理器)
