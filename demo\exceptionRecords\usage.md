# 异常记录管理模块

异常记录管理模块提供了完整的异常信息增删改查功能，包括：

## 功能特性

- **列表查询**: 支持按异常类型、处置状态、时间范围等条件查询
- **新增记录**: 独立页面进行异常记录新增
- **编辑记录**: 独立页面进行异常记录编辑
- **查看详情**: 独立页面查看异常记录详情
- **删除记录**: 支持单条记录删除
- **数据导出**: 支持查询结果导出

## 页面结构

- **列表页面**: `/exceptionRecords/list` - 显示异常记录列表
- **新增页面**: `/exceptionRecords/form?mode=add` - 新增异常记录
- **编辑页面**: `/exceptionRecords/form?mode=edit&id={id}` - 编辑异常记录
- **查看页面**: `/exceptionRecords/form?mode=view&id={id}` - 查看异常记录详情

## 字段说明

- **任务ID**: 关联的巡检任务ID
- **检查点ID**: 关联的检查点ID
- **异常类型**: 异常的分类类型
- **异常发生时间**: 异常发生的具体时间
- **异常描述**: 异常的详细描述
- **上报人**: 上报异常的人员信息
- **处置状态**: 异常的处置状态（未处置/已处置）
- **处置人**: 处置异常的人员信息
- **处置时间**: 异常处置的时间
- **处置方式**: 异常处置的具体方法
- **处置附件**: 处置过程中的相关附件

## 使用方式

```tsx
import ExceptionRecords from '@/pages/exceptionRecords';

// 在页面中使用
<ExceptionRecords history={history} location={location} />
```

## API 接口

模块使用 `src/service/exceptionRecordsApi.ts` 中定义的接口：

- `insert`: 新增异常记录
- `update`: 更新异常记录
- `deleteById`: 删除异常记录
- `getDetailById`: 获取异常记录详情
- `queryByPage`: 分页查询异常记录
- `exportByPage`: 导出异常记录

## 类型定义

使用 `src/types/exceptionRecords.ts` 中定义的类型：

- `ExceptionRecordsVo`: 异常记录视图对象
- `ExceptionRecordsInsertParam`: 新增参数
- `ExceptionRecordsUpdateParam`: 更新参数
- `ExceptionRecordsQueryParam`: 查询参数
- `ExceptionRecordsPageQueryParam`: 分页查询参数
- `ExceptionRecordsPageResponse`: 分页响应

## 样式定制

模块使用 `src/pages/exceptionRecords/exceptionRecords.module.less` 样式文件，支持自定义样式。

## 注意事项

1. 新增和编辑功能使用独立页面，而非弹窗形式
2. 支持响应式设计，在移动端有良好的显示效果
3. 表单验证完整，确保数据的有效性
4. 支持国际化，可根据需要扩展多语言支持

```tsx
import ExceptionRecords from '@/pages/exceptionRecords';

export default ExceptionRecords;
```
