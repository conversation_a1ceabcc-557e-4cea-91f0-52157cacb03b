# VideoPlayer 组件

基于 Video.js 的 React 视频播放器组件，提供了完整的视频播放功能和错误处理。

## 功能特性

- 基于 Video.js 7.20.3 构建
- 支持多种视频格式
- 响应式设计，自适应容器大小
- 完整的错误处理机制
- TypeScript 类型支持
- 播放器生命周期管理
- 可自定义样式和配置

## 使用方法

### 基本用法

```tsx
import React from 'react';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer';

const App: React.FC = () => {
  const videoOptions = {
    sources: [
      {
        src: 'https://example.com/video.mp4',
        type: 'video/mp4'
      }
    ]
  };

  return (
    <div>
      <VideoPlayer options={videoOptions} />
    </div>
  );
};
```

### 高级用法

```tsx
import React from 'react';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer';
import { VideoJsPlayer } from 'video.js';

const App: React.FC = () => {
  const videoOptions = {
    sources: [
      {
        src: 'https://example.com/video.mp4',
        type: 'video/mp4'
      },
      {
        src: 'https://example.com/video.webm',
        type: 'video/webm'
      }
    ],
    poster: 'https://example.com/poster.jpg',
    width: 800,
    height: 450,
    autoplay: false,
    muted: false,
    loop: false
  };

  const handlePlayerReady = (player: VideoJsPlayer) => {
    console.log('播放器已准备就绪', player);
    
    // 可以在这里添加自定义事件监听
    player.on('play', () => {
      console.log('视频开始播放');
    });
    
    player.on('pause', () => {
      console.log('视频暂停');
    });
  };

  const handlePlayerError = (error: any) => {
    console.error('播放器错误:', error);
    // 可以在这里添加错误处理逻辑
  };

  return (
    <div>
      <VideoPlayer 
        options={videoOptions}
        onReady={handlePlayerReady}
        onError={handlePlayerError}
        className="custom-video-player"
        style={{ maxWidth: '100%', height: 'auto' }}
      />
    </div>
  );
};
```

## Props 说明

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| options | VideoJsPlayerOptions | 是 | - | Video.js 播放器配置选项 |
| onReady | (player: VideoJsPlayer) => void | 否 | - | 播放器准备就绪时的回调函数 |
| onError | (error: VideoError) => void | 否 | - | 播放器错误时的回调函数 |
| className | string | 否 | '' | 自定义 CSS 类名 |
| style | React.CSSProperties | 否 | - | 自定义内联样式 |

## 配置选项

VideoPlayer 组件会自动合并以下默认配置：

```typescript
{
  controls: true,        // 显示控制栏
  responsive: true,      // 响应式设计
  fluid: true,          // 流体布局
  playbackRates: [0.5, 1, 1.25, 1.5, 2]  // 播放速度选项
}
```

你可以通过 `options` 属性覆盖这些默认配置。

## 注意事项

1. **依赖管理**: 确保项目中已安装 `video.js` 依赖
2. **样式引入**: 组件会自动引入 Video.js 的默认样式
3. **内存管理**: 组件会自动处理播放器的创建和销毁，避免内存泄漏
4. **错误处理**: 建议提供 `onError` 回调来处理播放错误
5. **可访问性**: 组件包含了基本的可访问性支持

## 常见问题

### 1. 视频无法播放

检查视频源是否可访问，格式是否支持：

```tsx
const options = {
  sources: [
    {
      src: 'your-video-url.mp4',
      type: 'video/mp4'
    }
  ]
};
```

### 2. 自定义样式

可以通过 className 和 style 属性自定义样式：

```tsx
<VideoPlayer 
  options={options}
  className="my-custom-player"
  style={{ border: '1px solid #ccc' }}
/>
```

### 3. 播放器事件监听

在 `onReady` 回调中添加事件监听：

```tsx
const handleReady = (player: VideoJsPlayer) => {
  player.on('loadstart', () => console.log('开始加载'));
  player.on('canplay', () => console.log('可以播放'));
  player.on('ended', () => console.log('播放结束'));
};
```

## 更新日志

- v1.0.0: 初始版本，基本播放功能
- v1.1.0: 添加错误处理和 TypeScript 支持
- v1.2.0: 优化内存管理和播放器生命周期
