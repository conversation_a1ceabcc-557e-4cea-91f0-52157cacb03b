import React, { useEffect, useRef, useState } from 'react';
import videojs, { VideoJsPlayer, VideoJsPlayerOptions } from 'video.js';
import 'video.js/dist/video-js.css';

// 定义错误类型
interface VideoError {
  code?: number;
  message?: string;
  [key: string]: unknown;
}

// 定义 VideoPlayer 组件的 props 类型
interface VideoPlayerProps {
  options: VideoJsPlayerOptions;
  onReady?: (player: VideoJsPlayer) => void;
  onError?: (error: VideoError) => void;
  className?: string;
  style?: React.CSSProperties;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  options,
  onReady,
  onError,
  className = '',
  style,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<VideoJsPlayer | null>(null);
  const [isPlayerReady, setIsPlayerReady] = useState<boolean>(false);

  useEffect(() => {
    // 确保 DOM 元素存在且播放器未初始化
    if (videoRef.current && !playerRef.current) {
      // 合并默认配置和传入的配置
      const playerOptions: VideoJsPlayerOptions = {
        controls: true,
        responsive: true,
        fluid: true,
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        ...options,
      };

      // 初始化 Video.js 播放器
      const player: VideoJsPlayer = videojs(videoRef.current, playerOptions, () => {
        setIsPlayerReady(true);
        onReady?.(player);
      });

      // 保存播放器实例引用
      playerRef.current = player;

      // 添加错误处理
      player.on('error', (error: VideoError) => {
        onError?.(error);
      });

      // 返回清理函数
      return () => {
        if (playerRef.current && !playerRef.current.isDisposed()) {
          playerRef.current.dispose();
          playerRef.current = null;
          setIsPlayerReady(false);
        }
      };
    }

    // 如果没有初始化播放器，返回 undefined
    return undefined;
  }, []); // 移除 options 依赖，避免重复初始化

  // 当 options 变化时更新播放器配置
  useEffect(() => {
    if (playerRef.current && isPlayerReady && options.sources) {
      playerRef.current.src(options.sources);
    }
  }, [options.sources, isPlayerReady]);

  return (
    <div data-vjs-player style={style}>
      <video
        ref={videoRef}
        className={`video-js vjs-default-skin ${className}`}
        data-setup="{}"
      />
    </div>
  );
};

export default VideoPlayer;