.exception-records-form {
  .form {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .ant-form-item-label {
      text-align: left;
    }
    
    .ant-input,
    .ant-select,
    .ant-picker {
      border-radius: 4px;
    }
    
    .ant-input:focus,
    .ant-select:focus,
    .ant-picker:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.exception-records-row-operator {
  .ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1;
  }
}

.required-item {
  .ant-input,
  .ant-select-selector,
  .ant-picker {
    border-color: #ff4d4f !important;
  }
}

.search-btn {
  background-color: #1890ff;
  border-color: #1890ff;
  
  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }
}

.reset-btn {
  &:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }
}

.lable-col {
  display: flex;
  align-items: center;
  
  .lable-form-item {
    margin-bottom: 0;
    
    .ant-form-item-label {
      padding-bottom: 0;
    }
  }
  
  .center-label-form-item {
    margin-bottom: 0;
    
    .ant-form-item-label {
      text-align: center;
      padding-bottom: 0;
    }
  }
}

.start-time-item {
  padding-right: 8px;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
  color: #262626;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .back-btn {
      margin-right: 12px;
      color: #1890ff;
      
      &:hover {
        color: #40a9ff;
      }
    }
    
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .header-right {
    .ant-btn {
      margin-left: 8px;
    }
  }
}

.form-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #262626;
    border-left: 3px solid #1890ff;
    padding-left: 8px;
  }
}

.form-row {
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mr-8 {
  margin-right: 8px;
}

.ml-8 {
  margin-left: 8px;
}

.p-16 {
  padding: 16px;
}

.border-top {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.status-badge {
  &.status-0 {
    color: #faad14;
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  &.status-1 {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}

.exception-type-tag {
  color: #f50;
  background-color: #fff2e8;
  border: 1px solid #ffbb96;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.time-display {
  color: #666;
  font-size: 12px;
}

.description-text {
  color: #262626;
  line-height: 1.5;
  word-break: break-all;
}

.user-info {
  color: #1890ff;
  cursor: pointer;
  
  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
}

.file-list {
  .file-item {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
    
    &:hover {
      background-color: #e6f7ff;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-data {
  text-align: center;
  color: #999;
  padding: 40px 0;
  
  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

.operation-buttons {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #e8e8e8;
  
  .ant-btn {
    margin: 0 8px;
    min-width: 80px;
  }
}

@media (max-width: 768px) {
  .exception-records-form {
    padding: 12px;
    
    .form {
      .ant-col {
        width: 100% !important;
        max-width: 100% !important;
      }
    }
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    
    .header-right {
      margin-top: 12px;
      width: 100%;
      
      .ant-btn {
        width: 100%;
        margin: 4px 0;
      }
    }
  }
  
  .operation-buttons {
    .ant-btn {
      width: 100%;
      margin: 4px 0;
    }
  }
}
