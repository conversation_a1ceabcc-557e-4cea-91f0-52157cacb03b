import React from 'react';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import ExceptionRecordsRoute from './index';

/**
 * @description 异常记录管理测试组件
 */
const ExceptionRecordsTest: React.FC = () => {
  // 模拟 history 对象
  const mockHistory = {
    push: (path: string) => {
      console.log('Navigate to:', path);
    },
    goBack: () => {
      console.log('Go back');
    },
  };

  // 模拟 location 对象
  const mockLocation = {
    pathname: '/exceptionRecords/list',
    search: '',
  };

  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <ExceptionRecordsRoute history={mockHistory} location={mockLocation} />
    </div>
  );
};

export default YTHLocalization.withLocal(
  ExceptionRecordsTest,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
